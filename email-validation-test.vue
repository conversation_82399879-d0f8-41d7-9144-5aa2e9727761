<template>
  <div class="p-6 max-w-md mx-auto">
    <h2 class="text-xl font-bold mb-4">Email Validation Test</h2>
    
    <VeeForm v-slot="{ meta, errors }" @submit="onSubmit">
      <VeeField
        v-slot="{ field, errorMessage }"
        name="email"
        rules="email_required"
      >
        <DInput
          v-bind="field"
          type="email"
          label="Email"
          placeholder="Enter email address"
          required
          :error="errorMessage"
        />
      </VeeField>

      <div class="mt-4">
        <button
          type="submit"
          :disabled="!meta.valid"
          class="w-full px-4 py-2 bg-blue-500 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-600 transition"
        >
          Submit
        </button>
      </div>

      <div class="mt-4 p-4 bg-gray-100 rounded-lg">
        <h3 class="font-semibold mb-2">Form State:</h3>
        <p><strong>Valid:</strong> {{ meta.valid }}</p>
        <p><strong>Touched:</strong> {{ meta.touched }}</p>
        <p><strong>Dirty:</strong> {{ meta.dirty }}</p>
        <p><strong>Errors:</strong> {{ JSON.stringify(errors) }}</p>
      </div>

      <div class="mt-4">
        <h3 class="font-semibold mb-2">Test Cases:</h3>
        <div class="space-y-2">
          <button
            type="button"
            @click="setTestEmail('')"
            class="block w-full px-3 py-2 bg-gray-200 rounded hover:bg-gray-300 transition"
          >
            Test: Empty Email
          </button>
          <button
            type="button"
            @click="setTestEmail('invalid-email')"
            class="block w-full px-3 py-2 bg-gray-200 rounded hover:bg-gray-300 transition"
          >
            Test: Invalid Email
          </button>
          <button
            type="button"
            @click="setTestEmail('<EMAIL>')"
            class="block w-full px-3 py-2 bg-gray-200 rounded hover:bg-gray-300 transition"
          >
            Test: Valid Email
          </button>
        </div>
      </div>
    </VeeForm>
  </div>
</template>

<script setup lang="ts">
import { useForm } from 'vee-validate';

const { setFieldValue } = useForm();

const setTestEmail = (email: string) => {
  setFieldValue('email', email);
};

const onSubmit = (values: any) => {
  console.log('Form submitted with values:', values);
  alert(`Form submitted with email: ${values.email}`);
};
</script>

<style>
/* Add any custom styles here */
</style>
</template>
