<template>
  <aside
    class="bg-lightMood-2 fixed top-0 w-[240px] z-50 h-full py-5 px-3"
    :class="{ 'right-0': isRtl, 'left-0': !isRtl }"
  >
    <div class="flex flex-col w-full">
      <div class="flex items-center justify-center gap-3 px-1 mb-8">
        <img
          src="@/assets/images/logo.svg"
          alt="Logo"
          width="50px"
          height="49px"
          aria-label="Application Logo"
        />
        <span class="text-xl">Etirda</span>
      </div>

      <nav class="flex flex-col w-full">
        <ul class="space-y-3">
          <router-link v-slot="{ isActive, navigate }" to="/" custom>
            <li
              @click="navigate"
              role="menuitem"
              class="px-1 py-2 text-neutral-5 rounded-lg transition-colors duration-200 cursor-pointer hover:text-primary hover:bg-transparent"
              :class="{
                'bg-primary/20 hover:text-neutral-5 hover:bg-primary/20 ':
                  isActive,
                'bg-transparent': !isActive,
              }"
            >
              <div class="flex items-center">
                <DaryaAccountsIcon />
                <span
                  class="text-base font-medium"
                  :class="{ 'mr-3': isRtl, 'ml-3': !isRtl }"
                >
                  {{ t('common.accounts') }}
                </span>
              </div>
            </li>
          </router-link>

          <router-link
            v-if="firstActiveAccount"
            v-slot="{ isActive, navigate }"
            :to="`/account/${firstActiveAccount.id}/account-overview`"
            custom
          >
            <li
              @click="navigate"
              role="menuitem"
              class="px-1 py-2 text-neutral-5 rounded-lg transition-colors duration-200 cursor-pointer hover:text-primary hover:bg-transparent"
              :class="{
                'bg-primary/20': isActive,
                'bg-transparent': !isActive,
              }"
            >
              <div class="flex items-center">
                <DaryaOutlineCategoryIcon />
                <span
                  class="text-base font-medium"
                  :class="{ 'mr-3': isRtl, 'ml-3': !isRtl }"
                >
                  {{ t('sidebar.account_overview') }}
                </span>
              </div>
            </li>
          </router-link>
          <router-link
            v-if="firstActiveAccount"
            v-slot="{ isActive, navigate }"
            :to="`/account/${firstActiveAccount.id}/trading-overview`"
            custom
          >
            <li
              @click="navigate"
              role="menuitem"
              class="px-1 py-2 text-neutral-5 rounded-lg transition-colors duration-200 cursor-pointer hover:text-primary"
              :class="{
                'bg-primary/20': isActive,
                'bg-transparent': !isActive,
              }"
            >
              <div class="flex items-center">
                <DaryaOutlineCandleIcon />
                <span
                  class="text-base font-medium"
                  :class="{ 'mr-3': isRtl, 'ml-3': !isRtl }"
                >
                  {{ t('sidebar.trading_overview') }}
                </span>
              </div>
            </li>
          </router-link>
          <router-link
            v-slot="{ isActive, navigate }"
            to="/transactions"
            custom
          >
            <li
              @click="navigate"
              role="menuitem"
              class="px-1 py-2 text-neutral-5 rounded-lg transition-colors duration-200 cursor-pointer hover:text-primary"
              :class="{
                'bg-primary/20': isActive,
                'bg-transparent ': !isActive,
              }"
            >
              <div class="flex items-center">
                <DaryaOutlineBitcoinConvertIcon />
                <span
                  class="text-base font-medium"
                  :class="{ 'mr-3': isRtl, 'ml-3': !isRtl }"
                >
                  {{ t('sidebar.transactions') }}
                </span>
              </div>
            </li>
          </router-link>
          <router-link v-slot="{ isActive, navigate }" to="/withdraw" custom>
            <li
              @click="navigate"
              role="menuitem"
              class="px-1 py-2 text-neutral-5 rounded-lg transition-colors duration-200 cursor-pointer hover:text-primary"
              :class="{
                'bg-primary/20': isActive,
                'bg-transparent': !isActive,
              }"
            >
              <div class="flex items-center">
                <DaryaOutlineCardIcon />
                <span
                  class="text-base font-medium"
                  :class="{ 'mr-3': isRtl, 'ml-3': !isRtl }"
                >
                  {{ t('sidebar.withdrawal') }}
                </span>
              </div>
            </li>
          </router-link>
          <div class="border-b border-neutral-12 py-1"></div>
          <router-link v-slot="{ isActive, navigate }" to="/challenges" custom>
            <li
              @click="navigate"
              role="menuitem"
              class="px-1 py-2 text-neutral-5 rounded-lg transition-colors duration-200 cursor-pointer hover:text-primary"
              :class="{
                'bg-primary/20': isActive,
                'bg-transparent': !isActive,
              }"
            >
              <div class="flex items-center">
                <DaryaOutlineRankingIcon />
                <span
                  class="text-base font-medium"
                  :class="{ 'mr-3': isRtl, 'ml-3': !isRtl }"
                >
                  {{ t('sidebar.challenges') }}
                </span>
              </div>
            </li>
          </router-link>
          <router-link v-slot="{ isActive, navigate }" to="/competition" custom>
            <li
              @click="navigate"
              role="menuitem"
              class="px-1 py-2 text-neutral-5 rounded-lg transition-colors duration-200 cursor-pointer hover:text-primary"
              :class="{
                'bg-primary/20': isActive,
                'bg-transparent': !isActive,
              }"
            >
              <div class="flex items-center">
                <DaryaOutlineCupIcon />
                <span
                  class="text-base font-medium"
                  :class="{ 'mr-3': isRtl, 'ml-3': !isRtl }"
                >
                  {{ t('sidebar.competition') }}
                </span>
              </div>
            </li>
          </router-link>
          <router-link
            v-slot="{ isActive, navigate }"
            to="/certifications"
            custom
          >
            <li
              @click="navigate"
              role="menuitem"
              class="px-1 py-2 text-neutral-5 rounded-lg transition-colors duration-200 cursor-pointer hover:text-primary"
              :class="{
                'bg-primary/20': isActive,
                'bg-transparent': !isActive,
              }"
            >
              <div class="flex items-center">
                <DaryaOutlineMedalStarIcon />
                <span
                  class="text-base font-medium"
                  :class="{ 'mr-3': isRtl, 'ml-3': !isRtl }"
                >
                  {{ t('sidebar.certifications') }}
                </span>
              </div>
            </li>
          </router-link>
          <div class="border-b border-neutral-12 py-1"></div>
          <router-link v-slot="{ isActive, navigate }" to="/tools" custom>
            <li
              @click="navigate"
              role="menuitem"
              class="px-1 py-2 text-neutral-5 rounded-lg transition-colors duration-200 cursor-pointer hover:text-primary"
              :class="{
                'bg-primary/20': isActive,
                'bg-transparent': !isActive,
              }"
            >
              <div class="flex items-center">
                <DaryaOutlineColorSwatchIcon />
                <span
                  class="text-base font-medium"
                  :class="{ 'mr-3': isRtl, 'ml-3': !isRtl }"
                >
                  {{ t('sidebar.tools') }}
                </span>
              </div>
            </li>
          </router-link>
          <router-link v-slot="{ isActive, navigate }" to="/calculator" custom>
            <li
              @click="navigate"
              role="menuitem"
              class="px-1 py-2 text-neutral-5 rounded-lg transition-colors duration-200 cursor-pointer hover:text-primary"
              :class="{
                'bg-primary/20': isActive,
                'bg-transparent': !isActive,
              }"
            >
              <div class="flex items-center">
                <DaryaOutlineCalculatorIcon />
                <span
                  class="text-base font-medium"
                  :class="{ 'mr-3': isRtl, 'ml-3': !isRtl }"
                >
                  {{ t('sidebar.calculator') }}
                </span>
              </div>
            </li>
          </router-link>
          <div class="fixed bottom-4">
            <router-link v-slot="{ isActive, navigate }" to="/setting" custom>
              <li
                @click="navigate"
                role="menuitem"
                class="px-1 py-2 text-neutral-5 rounded-lg transition-colors duration-200 cursor-pointer hover:text-primary"
                :class="{
                  'bg-primary/20': isActive,
                  'bg-transparent': !isActive,
                }"
              >
                <div class="flex items-center">
                  <DaryaOutlineSetting2Icon />
                  <span
                    class="text-base font-medium"
                    :class="{ 'mr-3': isRtl, 'ml-3': !isRtl }"
                  >
                    {{ t('sidebar.setting') }}
                  </span>
                </div>
              </li>
            </router-link>
            <router-link v-slot="{ isActive, navigate }" to="/faq" custom>
              <li
                @click="navigate"
                role="menuitem"
                class="px-1 py-2 text-neutral-5 rounded-lg transition-colors duration-200 cursor-pointer hover:text-primary"
                :class="{
                  'bg-primary/20': isActive,
                  'bg-transparent': !isActive,
                }"
              >
                <div class="flex items-center">
                  <DaryaOutlineMessageQuestionIcon />
                  <span
                    class="text-base font-medium"
                    :class="{ 'mr-3': isRtl, 'ml-3': !isRtl }"
                  >
                    {{ t('sidebar.faq') }}
                  </span>
                </div>
              </li>
            </router-link>
          </div>
        </ul>
      </nav>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { computed, toRaw } from 'vue';
import { useI18n } from 'vue-i18n';

import { useLocale } from '@/composables/useLocale';
import { useGetAllAccountByUserId } from '@/modules/account';
import { useAuth } from '@/modules/user';
const { user } = useAuth();

const { allAccountData } = useGetAllAccountByUserId(
  user.value?.nameid ?? '',
  true
);
const firstActiveAccount = computed(() => {
  if (allAccountData.value) {
    return allAccountData.value?.find(
      (account) => String(account.status) === '1'
    );
  }
  return null;
});
// const firstActiveAccount = allAccountData?.value.find(
//   (account) => String(account.status) === '1'
// );
const { t } = useI18n();
const { isRtl } = useLocale();
</script>
