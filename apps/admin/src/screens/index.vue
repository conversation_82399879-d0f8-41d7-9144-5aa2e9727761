<template>
  <span class="font-medium"> User Overview </span>

  <div class="mb-4 flex" dir="rtl">
    <DButton
      label="Add user"
      icon="DaryaPlusIcon"
      icon-position="right"
      class="cursor-pointer"
      @click="isOpenAddUser = true"
    />
  </div>

  <!-- User Data with State Management -->
  <DataStateWrapper
    :isLoading="userState.isLoading.value"
    :hasError="userState.hasError.value"
    :hasNoData="userState.hasNoData.value"
    :isRefetching="userState.isRefetching.value"
    :errorConfig="userState.errorConfig.value"
    :emptyConfig="userState.emptyConfig.value"
    :onRetry="retryUserData"
  >
    <DBox class="flex w-full pt-4">
      <DBaseTable
        :columns="columns"
        :data="userList"
        :enable-pagination="true"
        :enable-search="true"
        :showRowsPerPageSelect="true"
      >
        <template #column-fullName="{ row }">
          <span class="flex flex-col">
            <span>{{ row.firstName }} {{ row.lastName }}</span>
            <span class="text-sm">{{ row.email }}</span>
          </span>
        </template>
        <template #column-roles="{ row }">
          <span class="text-sm" v-for="item in row.roles" :key="item">
            {{ item }}
          </span>
        </template>
        <template #column-country="{ row }">
          <span class="flex flex-col">
            <span class="text-sm">{{ row.country?.fullName }}</span>
          </span>
        </template>
        <template #column-verification="{ row }">
          <DChip :label="row.isActive" color="gray" />
        </template>
        <template #column-actions="{ row }">
          <DPopper
            v-model="dropdownState[row.id]"
            placement="bottom-end"
            :arrow="false"
            offset-y="6"
            append-to-body
          >
            <template #default>
              <DButton
                icon="DaryaArrowDownIcon"
                icon-position="right"
                class="cursor-pointer"
                label="Action"
              />
            </template>

            <template #content>
              <div
                class="bg-white shadow-lg border border-gray-200 rounded-md w-48"
                :class="isRtl ? 'text-right' : 'text-left'"
              >
                <ul>
                  <li
                    class="flex items-center p-3 hover:bg-gray-300 cursor-pointer border-b border-gray-200"
                    @click="handleOpenEditDialog(row)"
                  >
                    <DaryaEyeIcon size="16" color="gray" />
                    <span class="ms-1 text-gray-500">Edit Profile</span>
                  </li>

                  <li
                    v-if="isUserRule(row)"
                    class="flex items-center p-3 hover:bg-gray-300 cursor-pointer border-b border-gray-200"
                    @click="handleOpenAddAccountDialog(row)"
                  >
                    <DaryaPlusIcon size="16" color="gray" />
                    <span class="ms-1 text-gray-500">Add Account</span>
                  </li>
                  <li
                    v-if="isUserRule(row)"
                    class="flex items-center p-3 hover:bg-gray-300 cursor-pointer border-b border-gray-200"
                    @click="handleShowAccounts(row)"
                  >
                    <DaryaPlusIcon size="16" color="gray" />
                    <span class="ms-1 text-gray-500">Show Accounts</span>
                  </li>
                  <li
                    class="flex items-center p-3 hover:bg-gray-300 cursor-pointer"
                    @click="handleOpenDeleteDialog(row)"
                  >
                    <DaryaTrashLightIcon size="16" color="gray" />
                    <span class="ms-1 text-gray-500">Delete user</span>
                  </li>
                </ul>
              </div>
            </template>
          </DPopper>
        </template>
      </DBaseTable>
    </DBox>
  </DataStateWrapper>

  <CreateUserDialog
    :isOpenAddUser="isOpenAddUser"
    @update:isOpenAddUser="isOpenAddUser = $event"
    @close="handleCloseCreateDialog"
  />
  <DeleteUserDialog
    :isOpenDeleteUser="isOpenDeleteUser"
    :userId="userId"
    @update:isOpenAddUser="isOpenDeleteUser = $event"
    @close="handleCloseDeleteDialog"
  />
  <EditUserDialog
    :isOpenEditUser="isOpenEditUser"
    :userId="userId"
    @update:isOpenEditUser="isOpenEditUser = $event"
    @close="handleCloseEditDialog"
  />
  <CreateAccountDialog
    :isOpenAddAccount="isOpenAddAccount"
    :userId="userId"
    :userName="userName"
    @update:isOpenAddAccount="isOpenAddAccount = $event"
    @close="handleCloseAddAccountDialog"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';

import {
  useGetAllUser,
  CreateUserDialog,
  DeleteUserDialog,
  EditUserDialog,
} from '@/modules/user/index';

import { CreateAccountDialog } from '@/modules/account';
import { useRouter } from 'vue-router';

import { useLocale } from '@/composables/useLocale';
import DataStateWrapper from '@/components/DataStateWrapper.vue';
import { useDataState } from '@/composables/useDataState';

const userId = ref();
const userName = ref();
const isOpenDeleteUser = ref(false);
const isOpenAddUser = ref(false);
const isOpenEditUser = ref(false);
const isOpenAddAccount = ref(false);
const router = useRouter();

// User data with state management
const { userList, getAllUser, status, isFetching, isError, error } =
  useGetAllUser();

const userState = useDataState(
  status,
  userList,
  isFetching,
  isError,
  error,
  'users' // Using custom 'user-data' type for specific message
);

// Retry function
const retryUserData = () => getAllUser();

const columns = ref([
  { key: 'fullName', label: 'Name' },
  { key: 'roles', label: 'User Type' },
  { key: 'address', label: 'Address' },
  { key: 'country', label: 'Country' },
  { key: 'city', label: 'City' },
  { key: 'postalCode', label: 'Postal Code' },
  { key: 'phoneNumber', label: 'Phone Number' },
  { key: 'phoneNumberWithoutExt', label: 'Phone Number WithoutExt' },
  { key: 'phoneNumberConfirmed', label: 'Phone Number Confirmed' },
  { key: 'twoFactorEnabled', label: 'Two Factor Enabled' },
  { key: 'lockoutEnd', label: 'Lockout End' },
  { key: 'lockoutEnabled', label: 'Lockout Enabled' },
  { key: 'emailConfirmed', label: 'Email Confirmed' },
  { key: 'isActive', label: 'Is Active' },
  { key: 'accessFailedCount', label: 'Access Failed Count' },
  { key: 'actions', label: 'Actions' },
]);

const { isRtl } = useLocale();
const dropdownState = ref<Record<string, boolean>>({});

const handleOpenDeleteDialog = (item) => {
  isOpenDeleteUser.value = true;
  userId.value = item.id;
  dropdownState.value[item.id] = false;
};
const handleOpenEditDialog = (item) => {
  console.log('Opening edit dialog for item:', item);

  // Validate item data
  if (!item || !item.id) {
    console.error('Invalid item data for edit dialog:', item);
    return;
  }

  // Close dropdown first
  dropdownState.value[item.id] = false;

  // Set userId before opening dialog to prevent race condition
  userId.value = item.id;

  // Add small delay to ensure userId is set before dialog opens
  setTimeout(() => {
    console.log('Opening edit dialog with userId:', userId.value);
    isOpenEditUser.value = true;
  }, 10);
};
const handleOpenAddAccountDialog = (item) => {
  userId.value = item.id;
  userName.value = `${item.firstName + item.lastName}`;
  isOpenAddAccount.value = true;
};

const handleCloseEditDialog = () => {
  console.log('Closing edit dialog');
  isOpenEditUser.value = false;

  // Reset userId after dialog closes
  setTimeout(() => {
    userId.value = null;
  }, 100);

  // Refresh user list
  getAllUser();
};
const handleCloseDeleteDialog = () => {
  isOpenDeleteUser.value = false;
  getAllUser();
};
const handleCloseCreateDialog = () => {
  isOpenAddUser.value = false;
  getAllUser();
};
const handleCloseAddAccountDialog = () => {
  isOpenAddAccount.value = false;
};

const isUserRule = (item) => {
  return item.roleIds.includes('50e9ce86-158c-4d85-cbc1-08dd75d6f625');
};

const handleShowAccounts = (item) => {
  router.push({
    path: `/account/${item.id}/userAccounts`,
  });
};
</script>
