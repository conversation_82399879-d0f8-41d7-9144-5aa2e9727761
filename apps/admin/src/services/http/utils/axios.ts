import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

import objectToQueryString from './objectToQueryString';

const config: AxiosRequestConfig = {
  paramsSerializer: {
    serialize(params) {
      return objectToQueryString(params);
    },
  },
  timeout: 30000, // 30 second timeout
  headers: {
    'Content-Type': 'application/json',
  },
};

const axiosInstance: AxiosInstance = axios.create(config);

axiosInstance.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  return config;
});

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response.status === 401) {
      localStorage.removeItem('token');

      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export * as axios from 'axios';

export default axiosInstance;
