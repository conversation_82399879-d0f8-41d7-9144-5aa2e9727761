<template>
  <DDialog
    :open="isOpenAddAccount"
    :closeOnClickOutside="false"
    :title="`Add New account for ${userName}`"
    @update:open="emit('close')"
  >
    <template #body>
      <VeeForm v-slot="{ meta, handleSubmit }">
        <form
          autocomplete="off"
          @submit="handleSubmit($event, onSubmit)"
          class="space-y-6"
        >
          <VeeField v-slot="{ field, errorMessage }" name="planId">
            <DSelectBox
              v-bind="field"
              v-model="formModel.planId"
              label="Trading Program"
              :options="planListOptions"
              placeholder="Select a trading program"
              required
              :error="errorMessage"
            />
          </VeeField>
          <VeeField
            v-slot="{ field, errorMessage }"
            name="tradingPlatform"
            v-model="formModel.tradingPlatform"
          >
            <DInput
              v-bind="field"
              type="text"
              label="MetaTrader Version"
              placeholder="Enter the MetaTrader version"
              required
              :error="errorMessage"
            />
          </VeeField>

          <div class="flex justify-end pt-4">
            <DButton type="primary" label="Add  Trading Account" />
          </div>
        </form>
      </VeeForm>
    </template>
  </DDialog>

  <DToast />
</template>

<script setup lang="ts">
import { computed, reactive, toRef, unref } from 'vue';
import { useToast } from '@libs/darya-design-system';

import { useCreateAccountMutation } from '@/modules/account';
import { useGetAllPlan } from '@/modules/plan';

const emit = defineEmits(['close']);

const props = defineProps({
  isOpenAddAccount: Boolean,
  userId: String,
  userName: String,
});

const userIdRef = toRef(props, 'userId');
const formModel = reactive({
  userId: userIdRef,
  planId: 0,
  tradingPlatform: '',
});

const { planList } = useGetAllPlan();

const { addToast } = useToast();

const handleSuccess = () => {
  addToast({
    message: 'The user created!',
    type: 'success',
    position: 'right-top',
    duration: 3000,
  });

  emit('close');
};

const handleError = ({ error }) => {
  addToast({
    message: error ? error : 'Failed to create Account. Please try again.',
    type: 'error',
    position: 'right-top',
    duration: 3000,
  });
};
const planListOptions = computed(() => {
  return planList.value?.map((x) => ({ label: x.title, value: x.id })) ?? [];
});

const { createAccount } = useCreateAccountMutation({
  onSuccess: handleSuccess,
  onError: handleError,
});

const onSubmit = () => {
  createAccount({
    ...formModel,
    userId: unref(formModel.userId) || '',
  });
};
</script>
