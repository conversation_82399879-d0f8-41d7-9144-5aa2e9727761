import { computed } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getRoles } from '../request';

type UserRolesResponse = Awaited<ReturnType<typeof getRoles>>;

export default function useGetRoles() {
  const { status, isError, data, refetch } = useQuery<UserRolesResponse, Error>(
    {
      queryKey: ['roleList'],
      queryFn: getRoles,
    }
  );

  const roleList = computed(() => data.value?.data?.data ?? []);

  return {
    getRoles: refetch,
    roleList,
    isError,
    status,
  } as const;
}
