<template>
  <div
    :dir="textDirection"
    class="flex flex-col items-center w-full pt-14 overflow-y-auto h-screen"
  >
    <img src="@/assets/images/logo.svg" alt="Logo" class="w-24 h-24" />
    <span class="text-3xl font-semibold pt-5">{{ t('login.welcome') }}</span>

    <div class="w-full sm:mx-auto md:w-[450px]">
      <!-- Card Container -->

      <!-- Form Section -->
      <div class="flex flex-col gap-6 p-6 sm:px-12 md:p-7">
        <VeeForm v-slot="{ handleSubmit }" as="">
          <form
            class="w-full space-y-6"
            autocomplete="off"
            @submit="handleSubmit($event, onSubmit)"
          >
            <span v-if="isEmailState" class="text-sm text-center pb-2 block">
              {{ t('login.email_title') }}
            </span>

            <div v-else class="flex text-center flex-col pb-2">
              <span class="text-sm">{{ t('login.otp.title') }}</span>

              <div class="text-sm flex justify-center items-center">
                <span>{{ formModel.username }}</span>

                <button
                  type="button"
                  class="text-primary ml-2 cursor-pointer"
                  @click="switchToEmail"
                >
                  {{ t('login.otp.change_email') }}
                </button>
              </div>
            </div>

            <template v-if="isEmailState">
              <VeeField
                v-slot="{ field, errors }"
                name="email"
                rules="email_required"
              >
                <div class="flex flex-col">
                  <div
                    class="flex items-center gap-3 rounded-md border border-gray-300 bg-white p-3 transition-colors focus-within:border-primary focus-within:bg-blue-50"
                  >
                    <DaryaEnvolpeIcon size="20" />
                    <input
                      v-bind="field"
                      v-model="formModel.username"
                      type="email"
                      placeholder="UserName"
                      autocomplete="off"
                      autofocus
                      class="flex-1 border-none bg-transparent text-gray-800 placeholder:text-gray-600 focus:outline-none focus:ring-0"
                    />
                  </div>

                  <span v-if="errors.length" class="mt-1 text-sm text-red-500">
                    {{ errors[0] }}
                  </span>
                </div>
              </VeeField>

              <VeeField
                v-slot="{ field, errors }"
                name="password"
                rules="required"
              >
                <div class="flex flex-col">
                  <div
                    class="flex items-center gap-3 rounded-md border border-gray-300 bg-white p-3 transition-colors focus-within:border-primary focus-within:bg-blue-50"
                  >
                    <DaryaLockIcon size="20" />
                    <input
                      v-bind="field"
                      v-model="formModel.password"
                      type="password"
                      placeholder="Password"
                      autocomplete="off"
                      autofocus
                      class="flex-1 border-none bg-transparent text-gray-800 placeholder:text-gray-600 focus:outline-none focus:ring-0"
                    />
                  </div>

                  <span v-if="errors.length" class="mt-1 text-sm text-red-500">
                    {{ errors[0] }}
                  </span>
                </div>
              </VeeField>
            </template>

            <VeeField
              v-else
              v-slot="{ field, errors }"
              name="otp"
              rules="required"
            >
              <div class="flex flex-col">
                <div
                  class="flex items-center gap-3 rounded-md border border-gray-300 bg-white p-3 transition-colors focus-within:border-primary focus-within:bg-blue-50"
                >
                  <DaryaLockIcon size="20" />
                  <input
                    v-bind="field"
                    v-model="otpFormModel.otp"
                    type="text"
                    placeholder="Your Code"
                    autocomplete="off"
                    autofocus
                    class="flex-1 border-none bg-transparent text-gray-800 placeholder:text-gray-600 focus:outline-none focus:ring-0"
                  />
                </div>

                <span v-if="errors.length" class="mt-1 text-sm text-red-500">
                  {{ t('login.otp.validation_message') }}
                </span>
              </div>
            </VeeField>

            <DButton type="submit" class="flex w-full" :loading="isLoading">
              {{ t('common.submit') }}
            </DButton>
          </form>
        </VeeForm>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

import {
  useUserLoginMutation,
  useValidateOtpMutation,
} from '@/modules/user/index';

import { useLocale } from '@/composables/useLocale';

import LanguageSwitcher from '@/components/LanguageSwitcher.vue';

type FormState = 'email' | 'otp';

const { t } = useI18n();
const router = useRouter();
const { isRtl, textDirection } = useLocale();

const FORM_STATE = {
  EMAIL: 'email',
  OTP: 'otp',
} as const;

const formState = ref<FormState>(FORM_STATE.EMAIL);
const formModel = reactive({
  username: '',
  password: '',
});
const otpFormModel = reactive({
  userId: '',
  otp: '',
});

const isEmailState = computed(() => formState.value === FORM_STATE.EMAIL);
const isLoading = ref(false);

const switchToEmail = () => {
  formState.value = FORM_STATE.EMAIL;
};

const handleSuccess = ({ data }) => {
  otpFormModel.userId = data?.data;
  formState.value = FORM_STATE.OTP;
};

const handleSuccessVerifyOtp = () => {
  router.push('/');
};

const { loginUser } = useUserLoginMutation({
  onSuccess: (data) => {
    handleSuccess(data);
    isLoading.value = false;
  },
  onError: () => {
    isLoading.value = false;
  },
});
const { verifyOtp } = useValidateOtpMutation({
  onSuccess: handleSuccessVerifyOtp,
});

const onSubmit = () => {
  if (isEmailState.value) {
    isLoading.value = true;
    loginUser(formModel);
  
  } else {
    verifyOtp(otpFormModel);
  }
};
</script>
