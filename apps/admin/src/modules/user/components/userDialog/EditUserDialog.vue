<template>
  <DDialog
    :open="isOpenEditUser"
    :closeOnClickOutside="false"
    title="Edit User"
    @update:open="emit('close')"
    maxWidth="lg"
  >
    <template #body>
      <!-- User Data State Management -->
      <DataStateWrapper
        :isLoading="userDataState.isLoading.value"
        :hasError="userDataState.hasError.value"
        :hasNoData="userDataState.hasNoData.value"
        :isRefetching="userDataState.isRefetching.value"
        :errorConfig="userDataState.errorConfig.value"
        :emptyConfig="customEmptyConfig"
        :onRetry="retryUserData"
      >
        <VeeForm
          v-slot="{ meta, handleSubmit, errors }"
          :initial-values="initialFormValues"
        >
          <!-- Validation Summary -->
          <div
            v-if="Object.keys(errors).length > 0 && meta.touched"
            class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6"
          >
            <div class="flex items-center">
              <svg
                class="w-5 h-5 text-red-400 mr-2"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clip-rule="evenodd"
                />
              </svg>
              <h3 class="text-sm font-medium text-red-800">
                Please correct the following errors:
              </h3>
            </div>
            <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
              <li v-for="(error, field) in errors" :key="field">
                {{ error }}
              </li>
            </ul>
          </div>

          <form
            autocomplete="off"
            @submit="handleSubmit($event, onSubmit)"
            class="space-y-6"
          >
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <VeeField
                v-slot="{ field, errorMessage }"
                name="firstName"
                rules="required"
              >
                <DInput
                  v-bind="field"
                  v-model="formModel.firstName"
                  type="text"
                  label="First Name"
                  placeholder="Enter first name"
                  required
                  :error="errorMessage"
                />
              </VeeField>

              <VeeField
                v-slot="{ field, errorMessage }"
                name="lastName"
                rules="required"
              >
                <DInput
                  v-bind="field"
                  v-model="formModel.lastName"
                  type="text"
                  label="Last Name"
                  placeholder="Enter last name"
                  required
                  :error="errorMessage"
                />
              </VeeField>
            </div>

            <VeeField
              v-slot="{ field, errorMessage }"
              name="email"
              rules="email_required"
            >
              <DInput
                v-bind="field"
                v-model="formModel.email"
                type="email"
                label="Email"
                placeholder="Enter email address"
                required
                :error="errorMessage"
              />
            </VeeField>

            <VeeField
              v-slot="{ field, errorMessage }"
              name="phoneNumber"
              rules="phone_required"
            >
              <DInput
                v-bind="field"
                v-model="formModel.phoneNumberWithoutExt"
                type="tel"
                label="Phone Number"
                placeholder="Enter phone number"
                required
                :error="errorMessage"
              />
            </VeeField>

            <VeeField
              v-slot="{ field, errorMessage }"
              name="address"
              rules="required"
            >
              <DInput
                v-bind="field"
                v-model="formModel.address"
                type="text"
                label="Address"
                placeholder="Enter address"
                required
                :error="errorMessage"
              />
            </VeeField>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <VeeField
                v-slot="{ field, errorMessage }"
                name="city"
                rules="required"
              >
                <DInput
                  v-bind="field"
                  v-model="formModel.city"
                  type="text"
                  label="City"
                  placeholder="Enter city"
                  required
                  :error="errorMessage"
                />
              </VeeField>
              <VeeField
                v-slot="{ field, errorMessage }"
                name="postalCode"
                rules="postal_code"
              >
                <DInput
                  v-bind="field"
                  v-model="formModel.postalCode"
                  type="text"
                  label="Postal Code"
                  placeholder="Enter postal code"
                  required
                  :error="errorMessage"
                />
              </VeeField>

              <VeeField v-slot="{ field, errorMessage }" name="country">
                <DSelectBox
                  v-bind="field"
                  v-model="formModel.residenceCountryId"
                  label="Country"
                  :options="countryListOptions"
                  placeholder="Select country"
                  required
                  :error="errorMessage"
                />
              </VeeField>
              <VeeField
                v-slot="{ field, errorMessage }"
                name="role"
                rules="required|min:1"
              >
                <DSelectBox
                  :modelValue="field.value"
                  @update:modelValue="field.onChange"
                  label="Role"
                  :options="roleListOptions"
                  placeholder="Select role"
                  required
                  :error="errorMessage"
                  multiple
                />
              </VeeField>
            </div>

            <div class="flex justify-end pt-4 space-x-3">
              <!-- Submit Button with State Management -->
              <DButton
                type="submit"
                :styleType="updateSuccess ? 'success' : 'primary'"
                :label="getSubmitButtonLabel"
                :loading="isUpdating"
                :disabled="isUpdating"
              />
            </div>
          </form>
        </VeeForm>
      </DataStateWrapper>
    </template>
  </DDialog>

  <DToast />
</template>
<script setup lang="ts">
import { watch, reactive, toRef, computed, ref } from 'vue';
import { useToast } from '@libs/darya-design-system';
import { useField } from 'vee-validate';

import {
  useGetAllCountry,
  useGetRoles,
  useGetUserById,
  useUpdateUser,
} from '@/modules/user/index';

import DataStateWrapper from '@/components/DataStateWrapper.vue';
import { useDataState } from '@/composables/useDataState';
import { useForm } from 'vee-validate';

const props = defineProps<{
  isOpenEditUser: boolean;
  userId: string | null | undefined;
}>();
const emit = defineEmits(['close']);

const userIdStringRef = computed(() => props.userId ?? '');

// User data fetching with state management
const {
  getUserById,
  userData,
  status: userStatus,
  isFetching: userIsFetching,
  isError: userIsError,
} = useGetUserById(userIdStringRef, false);

// Watch for userId changes and log them
watch(
  userIdStringRef,
  (newUserId, oldUserId) => {
    console.log('UserId changed from', oldUserId, 'to', newUserId);
  },
  { immediate: true }
);

// Supporting data
const { countryList } = useGetAllCountry();
const { roleList } = useGetRoles();

// User data state management
const userDataState = useDataState(
  userStatus,
  userData,
  userIsFetching,
  userIsError,
  ref(null),
  'users'
);

// Custom empty state for user data
const customEmptyConfig = computed(() => ({
  title: 'User Not Found',
  message:
    'Unable to load user data. The user may have been deleted or you may not have permission to view it.',
  image: 'empty-state.svg',
  alt: 'User not found',
}));

// Retry function for user data
const retryUserData = () => {
  if (props.userId) {
    getUserById();
  }
};

const { addToast } = useToast();

// Form submission state management
const isUpdating = ref(false);
const updateError = ref<string | null>(null);
const updateSuccess = ref(false);

// VeeValidate form control
const { setFieldValue, setValues } = useForm();

const handleSuccess = (response) => {
  console.log('Update success:', response);

  // Reset states
  isUpdating.value = false;
  updateError.value = null;
  updateSuccess.value = true;

  // Show success message
  addToast({
    message: response?.message || 'User updated successfully!',
    type: 'success',
    position: 'right-top',
    duration: 3000,
  });

  // Refresh user data to show updated information
  if (props.userId) {
    getUserById();
  }

  // Close dialog after short delay to show success state
  setTimeout(() => {
    updateSuccess.value = false;
    emit('close');
  }, 1000);
};

const handleError = (error) => {
  console.error('Update error:', error);

  // Update error states
  isUpdating.value = false;
  updateError.value = error;
  updateSuccess.value = false;

  // Show error message
  addToast({
    message: error?.message || 'Failed to update user. Please try again.',
    type: 'error',
    position: 'right-top',
    duration: 5000,
  });

  // Don't close dialog on error so user can retry
};

const { updateUser } = useUpdateUser({
  onSuccess: handleSuccess,
  onError: handleError,
});

const countryListOptions = computed(() => {
  return (
    countryList.value?.map((x) => ({ label: x.fullName, value: x.id })) ?? []
  );
});

const roleListOptions = computed(() => {
  return roleList.value?.map((x) => ({ label: x.name, value: x.id })) ?? [];
});

// Submit button label based on state
const getSubmitButtonLabel = computed(() => {
  if (isUpdating.value) {
    return 'Updating...';
  }
  if (updateSuccess.value) {
    return 'Updated!';
  }
  if (updateError.value) {
    return 'Retry Update';
  }
  return 'Update User';
});

const formModel = reactive({
  firstName: '',
  lastName: '',
  email: '',
  phoneNumberWithoutExt: '',
  address: '',
  city: '',
  roleIds: [] as any[],
  postalCode: '',
  residenceCountryId: 0,
  isActive: true,
});

// Map form model to VeeValidate field names
const initialFormValues = computed(() => ({
  firstName: formModel.firstName,
  lastName: formModel.lastName,
  email: formModel.email,
  phoneNumber: formModel.phoneNumberWithoutExt,
  address: formModel.address,
  city: formModel.city,
  postalCode: formModel.postalCode,
  country: formModel.residenceCountryId,
  role: formModel.roleIds,
}));

const { value: roleValue } = useField('role');
watch(roleValue, (val) => {
  formModel.roleIds = Array.isArray(val) ? val : val ? [val] : [];
});

watch(
  () => props.isOpenEditUser,
  (isOpen) => {
    console.log(
      'Edit dialog open state changed:',
      isOpen,
      'userId:',
      props.userId
    );

    if (isOpen) {
      // Reset form states when dialog opens
      isUpdating.value = false;
      updateError.value = null;
      updateSuccess.value = false;

      // Validate userId before fetching data
      if (props.userId && props.userId.trim() !== '') {
        console.log('Fetching user data for userId:', props.userId);
        getUserById();
      } else {
        console.error(
          'EditUserDialog opened without valid userId:',
          props.userId
        );
        // Show error state
        updateError.value = 'No user ID provided';
      }
    }
  }
);

watch(
  userData,
  (newData) => {
    console.log('User data changed:', newData);
    if (newData) {
      console.log('Updating form model with user data');

      // Update form model
      Object.assign(formModel, {
        firstName: newData.firstName || '',
        lastName: newData.lastName || '',
        email: newData.email || '',
        phoneNumberWithoutExt: newData.phoneNumberWithoutExt || '',
        address: newData.address || '',
        city: newData.city || '',
        postalCode: newData.postalCode || '',
        residenceCountryId: newData.residenceCountryId || 0,
        roleIds: newData.roleIds || [],
        isActive: newData.isActive ?? true,
      });

      // Sync VeeValidate fields with the new data
      try {
        setFieldValue('firstName', newData.firstName || '');
        setFieldValue('lastName', newData.lastName || '');
        setFieldValue('email', newData.email || '');
        setFieldValue('phoneNumber', newData.phoneNumberWithoutExt || '');
        setFieldValue('address', newData.address || '');
        setFieldValue('city', newData.city || '');
        setFieldValue('postalCode', newData.postalCode || '');
        setFieldValue('country', newData.residenceCountryId || 0);
        setFieldValue('role', newData.roleIds || []);

        console.log('VeeValidate fields updated');
        console.log('Country value set to:', newData.residenceCountryId);
        console.log('Role value set to:', newData.roleIds);
      } catch (error) {
        console.error('Error updating VeeValidate fields:', error);
      }

      console.log('Form model updated:', formModel);
    } else {
      console.log('No user data received');
    }
  },
  { deep: true }
);

const onSubmit = (values) => {
  if (values && props.userId) {
    isUpdating.value = true;

    // Create update payload with user ID
    const updatePayload = {
      id: props.userId,
      ...formModel,
    };

    updateUser(updatePayload);
  } else {
    console.error('Form validation failed or missing user ID');
  }
};
</script>
