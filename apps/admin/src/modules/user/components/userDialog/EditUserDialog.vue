<template>
  <DDialog
    :open="isOpenEditUser"
    :closeOnClickOutside="false"
    title="Edit User"
    @update:open="handleDialogClose"
    maxWidth="lg"
  >
    <template #body>
      <DataStateWrapper
        :isLoading="userDataState.isLoading.value"
        :hasError="userDataState.hasError.value"
        :hasNoData="userDataState.hasNoData.value"
        :isRefetching="userDataState.isRefetching.value"
        :errorConfig="userDataState.errorConfig.value"
        :emptyConfig="emptyConfig"
        :onRetry="retryUserData"
      >
        <VeeForm
          v-slot="{ meta, handleSubmit, errors }"
          :initial-values="initialFormValues"
          @submit="handleFormSubmit"
        >
          <form
            autocomplete="off"
            @submit="handleSubmit($event, onSubmit)"
            class="space-y-6"
          >
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <VeeField
                v-slot="{ field, errorMessage }"
                name="firstName"
                rules="required"
              >
                <DInput
                  v-bind="field"
                  type="text"
                  label="First Name"
                  placeholder="Enter first name"
                  required
                  :error="errorMessage"
                />
              </VeeField>

              <VeeField
                v-slot="{ field, errorMessage }"
                name="lastName"
                rules="required"
              >
                <DInput
                  v-bind="field"
                  type="text"
                  label="Last Name"
                  placeholder="Enter last name"
                  required
                  :error="errorMessage"
                />
              </VeeField>
            </div>

            <VeeField
              v-slot="{ field, errorMessage }"
              name="email"
              rules="required|email"
            >
              <DInput
                v-bind="field"
                type="email"
                label="Email"
                placeholder="Enter email address"
                required
                :error="errorMessage"
              />
            </VeeField>

            <VeeField
              v-slot="{ field, errorMessage }"
              name="phoneNumber"
              rules="required"
            >
              <DInput
                v-bind="field"
                type="tel"
                label="Phone Number"
                placeholder="Enter phone number"
                required
                :error="errorMessage"
              />
            </VeeField>

            <VeeField
              v-slot="{ field, errorMessage }"
              name="address"
              rules="required"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Address"
                placeholder="Enter address"
                required
                :error="errorMessage"
              />
            </VeeField>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <VeeField
                v-slot="{ field, errorMessage }"
                name="city"
                rules="required"
              >
                <DInput
                  v-bind="field"
                  type="text"
                  label="City"
                  placeholder="Enter city"
                  required
                  :error="errorMessage"
                />
              </VeeField>

              <VeeField
                v-slot="{ field, errorMessage }"
                name="postalCode"
                rules="required"
              >
                <DInput
                  v-bind="field"
                  type="text"
                  label="Postal Code"
                  placeholder="Enter postal code"
                  required
                  :error="errorMessage"
                />
              </VeeField>

              <VeeField
                v-slot="{ field, errorMessage }"
                name="country"
                rules="required"
              >
                <DSelectBox
                  :modelValue="field.value"
                  label="Country"
                  :options="countryListOptions"
                  :placeholder="
                    countryError ? 'Unable to load countries' : 'Select country'
                  "
                  required
                  :error="
                    countryError ? 'Countries failed to load' : errorMessage
                  "
                  :disabled="countryError"
                  @update:modelValue="field.onChange"
                  @blur="field.onBlur"
                  @change="field.onChange"
                />
              </VeeField>

              <VeeField
                v-slot="{ field, errorMessage }"
                name="role"
                rules="required"
              >
                <DSelectBox
                  :modelValue="field.value"
                  label="Role"
                  :options="roleListOptions"
                  :placeholder="
                    roleError ? 'Unable to load roles' : 'Select role'
                  "
                  required
                  :error="roleError ? 'Roles failed to load' : errorMessage"
                  :disabled="roleError"
                  multiple
                  @update:modelValue="field.onChange"
                  @blur="field.onBlur"
                  @change="field.onChange"
                />
              </VeeField>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end pt-4 space-x-3">
              <DButton
                type="submit"
                styleType="primary"
                label="Update User"
                :loading="isUpdating"
                :disabled="isUpdating || !meta.valid || !meta.dirty"
              />
            </div>
          </form>
        </VeeForm>
      </DataStateWrapper>
    </template>
  </DDialog>

  <DToast />
</template>

<script setup lang="ts">
import { watch, toRef, computed, ref, nextTick } from 'vue';
import { useToast } from '@libs/darya-design-system';
import { useForm } from 'vee-validate';

import {
  useGetAllCountry,
  useGetRoles,
  useGetUserById,
  useUpdateUser,
} from '@/modules/user/index';

import DataStateWrapper from '@/components/DataStateWrapper.vue';
import { useDataState } from '@/composables/useDataState';

// Types
interface UserFormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  address: string;
  city: string;
  postalCode: string;
  country: number;
  role: string[];
}

interface UpdatePayload {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumberWithoutExt: string;
  address: string;
  city: string;
  postalCode: string;
  residenceCountryId: number;
  roleIds: string[];
  isActive: boolean;
}

// Props and Emits
const props = defineProps<{
  isOpenEditUser: boolean;
  userId: string | null | undefined;
}>();

const emit = defineEmits<{
  close: [];
}>();

// Reactive State
const isUpdating = ref(false);

// Computed Values
const userIdStringRef = computed(() => props.userId ?? '');
const isDialogOpen = toRef(props, 'isOpenEditUser');

// Data Fetching
const {
  getUserById,
  userData,
  status: userStatus,
  isFetching: userIsFetching,
  isError: userIsError,
} = useGetUserById(userIdStringRef, false);

const { countryList, isError: countryError } = useGetAllCountry(isDialogOpen);
const { roleList, isError: roleError } = useGetRoles(isDialogOpen);

// Data State Management
const userDataState = useDataState(
  userStatus,
  userData,
  userIsFetching,
  userIsError,
  ref(null),
  'users'
);

// Computed Options
const countryListOptions = computed(() => {
  if (countryError.value) {
    return [];
  }
  return (
    countryList.value?.map((x) => ({ label: x.fullName, value: x.id })) ?? []
  );
});

const roleListOptions = computed(() => {
  if (roleError.value) {
    return [];
  }
  return roleList.value?.map((x) => ({ label: x.name, value: x.id })) ?? [];
});

const initialFormValues = computed(() =>
  transformUserDataToFormData(userData.value)
);

const emptyConfig = computed(() => ({
  title: 'User Not Found',
  message:
    'Unable to load user data. The user may have been deleted or you may not have permission to view it.',
  image: 'empty-state.svg',
  alt: 'User not found',
}));

// Toast Notifications
const { addToast } = useToast();

// Helper Functions
const transformUserDataToFormData = (userData: any): UserFormData => ({
  firstName: userData?.firstName || '',
  lastName: userData?.lastName || '',
  email: userData?.email || '',
  phoneNumber: userData?.phoneNumberWithoutExt || '',
  address: userData?.address || '',
  city: userData?.city || '',
  postalCode: userData?.postalCode || '',
  country: userData?.residenceCountryId || 0,
  role: userData?.roleIds || [],
});

const transformFormDataToUpdatePayload = (
  formData: UserFormData
): UpdatePayload => ({
  id: props.userId!,
  firstName: formData.firstName,
  lastName: formData.lastName,
  email: formData.email,
  phoneNumberWithoutExt: formData.phoneNumber,
  address: formData.address,
  city: formData.city,
  postalCode: formData.postalCode,
  residenceCountryId: formData.country,
  roleIds: formData.role,
  isActive: true,
});

const retryUserData = () => {
  if (props.userId) {
    getUserById();
  }
};

// VeeValidate form control
const { resetForm, setValues } = useForm<UserFormData>();

// Success handler for user update
const handleSuccess = (response: any) => {
  isUpdating.value = false;

  addToast({
    message: response?.message || 'User updated successfully!',
    type: 'success',
    position: 'right-top',
    duration: 3000,
  });

  // Refresh user data and close dialog
  if (props.userId) {
    getUserById();
  }

  // Close dialog after a short delay to show success state
  setTimeout(() => {
    emit('close');
  }, 500);
};

const handleError = (error: any) => {
  isUpdating.value = false;

  addToast({
    message: error?.message || 'Failed to update user. Please try again.',
    type: 'error',
    position: 'right-top',
    duration: 5000,
  });
};

const handleFormSubmit = async (values: UserFormData) => {
  if (!props.userId) {
    addToast({
      message: 'User ID is missing. Cannot update user.',
      type: 'error',
      position: 'right-top',
      duration: 3000,
    });
    return;
  }

  try {
    isUpdating.value = true;
    const updatePayload = transformFormDataToUpdatePayload(values);
    await updateUser(updatePayload);
  } catch (error) {
    console.error('Form submission error:', error);
    handleError(error);
  }
};

const handleDialogClose = () => {
  if (!isUpdating.value) {
    emit('close');
  }
};

// User Update Mutation
const { updateUser } = useUpdateUser({
  onSuccess: handleSuccess,
  onError: handleError,
});

// Watchers
watch(
  () => props.isOpenEditUser,
  async (isOpen) => {
    if (isOpen) {
      isUpdating.value = false;
      if (props.userId?.trim()) {
        await getUserById();
      }
    } else {
      await nextTick();
      resetForm();
    }
  }
);

watch(
  userData,
  async (newData) => {
    if (newData && Object.keys(newData).length > 0) {
      const formData = transformUserDataToFormData(newData);

      await nextTick();

      setValues(formData);
    }
  },
  { deep: true }
);
</script>
