<template>
  <DDialog
    :open="isOpenAddUser"
    title="Add New User"
    :closeOnClickOutside="false"
    @update:open="emit('close')"
    maxWidth="lg"
  >
    <template #body>
      <VeeForm
        v-slot="{ meta, handleSubmit, errors }"
        :initial-values="initialFormValues"
      >
        <form
          autocomplete="off"
          @submit="handleSubmit($event, onSubmit)"
          class="space-y-6"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <VeeField
              v-slot="{ field, errorMessage }"
              name="firstName"
              rules="required"
            >
              <DInput
                v-bind="field"
                v-model="userFormModel.firstName"
                type="text"
                label="First Name"
                placeholder="Enter first name"
                required
                :error="errorMessage"
              />
            </VeeField>

            <VeeField
              v-slot="{ field, errorMessage }"
              name="lastName"
              rules="required"
            >
              <DInput
                v-bind="field"
                v-model="userFormModel.lastName"
                type="text"
                label="Last Name"
                placeholder="Enter last name"
                required
                :error="errorMessage"
              />
            </VeeField>
          </div>

          <VeeField
            v-slot="{ field, errorMessage }"
            name="email"
            rules="email_required"
          >
            <DInput
              v-bind="field"
              v-model="userFormModel.email"
              type="email"
              label="Email"
              placeholder="Enter email address"
              required
              :error="errorMessage"
            />
          </VeeField>

          <VeeField
            v-slot="{ field, errorMessage }"
            name="phoneNumber"
            rules="phone_required"
          >
            <DInput
              v-bind="field"
              v-model="userFormModel.phoneNumberWithoutExt"
              type="tel"
              label="Phone Number"
              placeholder="Enter phone number (e.g., +1234567890)"
              required
              :error="errorMessage"
            />
          </VeeField>

          <VeeField
            v-slot="{ field, errorMessage }"
            name="address"
            rules="required"
          >
            <DInput
              v-bind="field"
              v-model="userFormModel.address"
              type="text"
              label="Address"
              placeholder="Enter address"
              required
              :error="errorMessage"
            />
          </VeeField>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <VeeField
              v-slot="{ field, errorMessage }"
              name="city"
              rules="required"
            >
              <DInput
                v-bind="field"
                v-model="userFormModel.city"
                type="text"
                label="City"
                placeholder="Enter city"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="postalCode"
              rules="postal_code"
            >
              <DInput
                v-bind="field"
                v-model="userFormModel.postalCode"
                type="text"
                label="Postal Code"
                placeholder="Enter postal code"
                required
                :error="errorMessage"
              />
            </VeeField>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <VeeField
              v-slot="{ field, errorMessage }"
              name="country"
              rules="required"
            >
              <DSelectBox
                :modelValue="field.value"
                label="Country"
                :options="countryListOptions"
                placeholder="Select country"
                required
                :error="errorMessage"
                @update:modelValue="
                  (value) => {
                    field.onChange(value);
                    userFormModel.residenceCountryId = value;
                  }
                "
                @blur="field.onBlur"
              />
            </VeeField>

            <VeeField
              v-slot="{ field, errorMessage }"
              name="role"
              rules="required"
            >
              <DSelectBox
                :modelValue="field.value"
                label="Role"
                :options="roleListOptions"
                placeholder="Select role"
                required
                :error="errorMessage"
                multiple
                @update:modelValue="
                  (value) => {
                    field.onChange(value);
                    userFormModel.roleIds = value;
                  }
                "
                @blur="field.onBlur"
              />
            </VeeField>
          </div>

          <div class="flex justify-end pt-4">
            <DButton
              type="submit"
              styleType="primary"
              label="Create New Account"
              :loading="isSending"
              :disabled="!meta.valid || isSending"
            />
          </div>
        </form>
      </VeeForm>
    </template>
  </DDialog>

  <DToast />
  <DToast />
</template>

<script setup lang="ts">
import { computed, reactive, ref, toRef } from 'vue';
import { useToast } from '@libs/darya-design-system';

import {
  useGetRoles,
  useGetAllCountry,
  useCreateUserMutation,
} from '@/modules/user/index';
import type { UserFormModel } from '@/modules/user';
import { useForm } from 'vee-validate';

const emit = defineEmits(['close']);

const props = defineProps({
  isOpenAddUser: Boolean,
});

const userFormModel = reactive<UserFormModel>({
  firstName: '',
  lastName: '',
  email: '',
  phoneNumberWithoutExt: '',
  address: '',
  city: '',
  postalCode: '',
  residenceCountryId: 0,
  roleIds: [],
  permissionKeys: [],
  isActive: false,
  password: '',
});

// Only fetch roles and countries when dialog is open
const { roleList, isError: roleError, status: roleStatus } = useGetRoles(toRef(props, 'isOpenAddUser'));
const { countryList, isError: countryError } = useGetAllCountry(toRef(props, 'isOpenAddUser'));
const { addToast } = useToast();

const isSending = ref(false);

// VeeValidate form control
const { setFieldValue, resetForm } = useForm();
const handleSuccess = () => {
  isSending.value = false;

  addToast({
    message: 'The user created!',
    type: 'success',
    position: 'right-top',
    duration: 3000,
  });

  Object.assign(userFormModel, {
    role: '',
    firstName: '',
    lastName: '',
    email: '',
    phoneNumberWithoutExt: '',
    address: '',
    city: '',
    postalCode: '',
    residenceCountryId: '',
    roleIds: [''],
    permissionKeys: [''],
    isActive: false,
    password: '',
  });

  emit('close');
};

const handleError = ({ error }) => {
  isSending.value = false;

  Object.assign(userFormModel, {
    role: '',
    firstName: '',
    lastName: '',
    email: '',
    phoneNumberWithoutExt: '',
    address: '',
    city: '',
    postalCode: '',
    residenceCountryId: '',
    roleIds: [],
    permissionKeys: [''],
    isActive: false,
    password: '',
  });

  emit('close');

  addToast({
    message: error ? error : 'Failed to create user. Please try again.',
    type: 'error',
    position: 'right-top',
    duration: 3000,
  });
};

const { createUser } = useCreateUserMutation({
  onSuccess: handleSuccess,
  onError: handleError,
});

const roleListOptions = computed(() => {
  // If there's an error fetching roles, return empty array with error message
  if (roleError.value) {
    return [{ label: 'Error loading roles - please refresh', value: '', disabled: true }];
  }
  return roleList.value?.map((x) => ({ label: x.name, value: x.id })) ?? [];
});

const countryListOptions = computed(() => {
  // If there's an error fetching countries, return empty array with error message
  if (countryError.value) {
    return [{ label: 'Error loading countries - please refresh', value: '', disabled: true }];
  }
  return (
    countryList.value?.map((x) => ({ label: x.fullName, value: x.id })) ?? []
  );
});

// Map form model to VeeValidate field names
const initialFormValues = computed(() => ({
  firstName: userFormModel.firstName,
  lastName: userFormModel.lastName,
  email: userFormModel.email,
  phoneNumber: userFormModel.phoneNumberWithoutExt,
  address: userFormModel.address,
  city: userFormModel.city,
  postalCode: userFormModel.postalCode,
  country: userFormModel.residenceCountryId,
  role: userFormModel.roleIds,
}));

const onSubmit = () => {
  createUser(userFormModel);
  isSending.value = true;
};
</script>
