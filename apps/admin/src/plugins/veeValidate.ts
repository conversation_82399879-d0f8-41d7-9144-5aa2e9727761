import type { App } from 'vue';
import { Field, Form, configure, defineRule } from 'vee-validate';
import { isEmpty } from '@libs/utils';
import { i18n } from '@/plugins/i18n';

const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

const install = (Vue: App) => {
  configure({
    validateOnBlur: false,
    validateOnChange: false,
    validateOnModelUpdate: true,
    validateOnInput: false,
  });

  defineRule('required', (value) => {
    if (isEmpty(value)) {
      return i18n.global.t('common.validations.required');
    }

    return true;
  });

  defineRule('email_required', (value) => {
    if (isEmpty(value)) {
      return i18n.global.t('common.validations.email_required_message');
    }

    return emailRegex.test(value)
      ? true
      : i18n.global.t('common.validations.invalid_email_message');
  });

  defineRule('phone_required', (value) => {
    if (isEmpty(value)) {
      return i18n.global.t('common.validations.phone_required_message');
    }

    // Basic phone validation (can be enhanced based on requirements)
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(value.replace(/\s/g, ''))
      ? true
      : i18n.global.t('common.validations.invalid_phone_message');
  });

  defineRule('min_length', (value, [limit]) => {
    if (isEmpty(value)) {
      return true; // Let required rule handle empty values
    }

    if (value.length < limit) {
      return i18n.global.t('common.validations.min_length_message', {
        min: limit,
      });
    }

    return true;
  });

  defineRule('postal_code', (value) => {
    if (isEmpty(value)) {
      return i18n.global.t('common.validations.postal_code_required_message');
    }

    // Basic postal code validation (alphanumeric, spaces, hyphens)
    const postalCodeRegex = /^[A-Za-z0-9\s\-]{3,20}$/;
    return postalCodeRegex.test(value)
      ? true
      : i18n.global.t('common.validations.invalid_postal_code_message');
  });

  defineRule('min', (value, [limit]) => {
    if (isEmpty(value)) {
      return true; // Let required rule handle empty values
    }
    if (Array.isArray(value)) {
      if (value.length < limit) {
        return i18n.global.t('common.validations.min_length_message', {
          min: limit,
        });
      }
      return true;
    }
    // For strings
    if (typeof value === 'string' && value.length < limit) {
      return i18n.global.t('common.validations.min_length_message', {
        min: limit,
      });
    }
    return true;
  });

  Vue.component('VeeField', Field);
  Vue.component('VeeForm', Form);
};

export { install };
